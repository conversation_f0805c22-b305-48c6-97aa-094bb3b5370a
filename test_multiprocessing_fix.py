#!/usr/bin/env python3
"""
Test script to verify that the multiprocessing Chrome WebDriver fix works correctly.
This test creates multiple processes that each try to create a Chrome WebDriver instance
to ensure they don't conflict with each other.
"""

import multiprocessing
import tempfile
import os
import time
import subprocess
import sys
from app.services.browser import setup_browser

def test_browser_creation(process_id):
    """Test function that creates a browser instance in a separate process."""
    try:
        print(f"Process {process_id}: Starting browser creation")
        driver = setup_browser()
        
        # Verify the driver was created successfully
        print(f"Process {process_id}: <PERSON><PERSON><PERSON> created successfully")
        
        # Test basic functionality
        driver.get("https://www.google.com")
        title = driver.title
        print(f"Process {process_id}: Successfully loaded page with title: {title[:50]}...")
        
        # Clean up
        driver.quit()
        print(f"Process {process_id}: <PERSON><PERSON><PERSON> closed successfully")
        
        return True
    except Exception as e:
        print(f"Process {process_id}: Error - {str(e)}")
        return False

def test_multiprocessing_browsers():
    """Test that multiple processes can create browsers simultaneously without conflicts."""
    print("Testing multiprocessing browser creation...")
    
    # Test with 3 processes to simulate the multiprocessing scenario
    num_processes = 3
    
    with multiprocessing.Pool(processes=num_processes) as pool:
        # Create a list of process IDs
        process_ids = list(range(1, num_processes + 1))
        
        # Run the test function in parallel
        results = pool.map(test_browser_creation, process_ids)
    
    # Check results
    successful_processes = sum(results)
    print(f"\nResults: {successful_processes}/{num_processes} processes succeeded")
    
    if successful_processes == num_processes:
        print("✅ All processes successfully created and closed browsers!")
        return True
    else:
        print("❌ Some processes failed to create browsers")
        return False

def test_user_data_dir_cleanup():
    """Test that temporary user data directories are properly cleaned up."""
    print("\nTesting user data directory cleanup...")
    
    # Get the temp directory
    temp_dir = tempfile.gettempdir()
    
    # Count existing chrome_user_data directories before test
    before_count = len([d for d in os.listdir(temp_dir) if d.startswith('chrome_user_data_')])
    print(f"Chrome user data directories before test: {before_count}")
    
    # Create and close a browser
    driver = setup_browser()
    
    # Count directories during test (should be +1)
    during_count = len([d for d in os.listdir(temp_dir) if d.startswith('chrome_user_data_')])
    print(f"Chrome user data directories during test: {during_count}")
    
    # Close the browser
    driver.quit()
    
    # Wait a moment for cleanup
    time.sleep(1)
    
    # Count directories after test (should be back to original)
    after_count = len([d for d in os.listdir(temp_dir) if d.startswith('chrome_user_data_')])
    print(f"Chrome user data directories after test: {after_count}")
    
    if after_count == before_count:
        print("✅ User data directory cleanup working correctly!")
        return True
    else:
        print("❌ User data directory not cleaned up properly")
        return False

def run_cleanup():
    """Run the cleanup script before testing."""
    print("Running cleanup script...")
    try:
        result = subprocess.run([sys.executable, 'cleanup_chrome.py'],
                              capture_output=True, text=True, timeout=30)
        print(result.stdout)
        if result.stderr:
            print("Cleanup warnings:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running cleanup: {e}")
        return False

if __name__ == "__main__":
    print("Running Chrome WebDriver multiprocessing tests...\n")

    # Step 0: Run cleanup
    print("Step 0: Cleaning up any leftover Chrome processes and directories...")
    cleanup_ok = run_cleanup()
    if not cleanup_ok:
        print("⚠️  Cleanup had issues, but continuing with tests...")

    # Test 1: User data directory cleanup
    print("\nStep 1: Testing user data directory cleanup...")
    cleanup_success = test_user_data_dir_cleanup()

    # Test 2: Multiprocessing
    print("\nStep 2: Testing multiprocessing...")
    multiprocessing_success = test_multiprocessing_browsers()

    print("\n" + "="*50)
    print("FINAL RESULTS:")
    print(f"Cleanup: {'✅ PASS' if cleanup_ok else '⚠️  ISSUES'}")
    print(f"User data cleanup: {'✅ PASS' if cleanup_success else '❌ FAIL'}")
    print(f"Multiprocessing: {'✅ PASS' if multiprocessing_success else '❌ FAIL'}")

    if cleanup_success and multiprocessing_success:
        print("\n🎉 All tests passed! The multiprocessing fix is working correctly.")
        exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        exit(1)

#!/usr/bin/env python3
"""
Simple test to verify basic Chrome WebDriver functionality.
Run this first to ensure the basic setup works before testing multiprocessing.
"""

import os
import tempfile
import time
from app.services.browser import setup_browser

def test_basic_browser_creation():
    """Test basic browser creation and cleanup."""
    print("Testing basic browser creation...")
    
    try:
        # Create browser
        print("Creating browser...")
        driver = setup_browser()
        print("✅ Browser created successfully")
        
        # Test basic functionality
        print("Testing basic navigation...")
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✅ Successfully loaded page with title: {title[:50]}...")
        
        # Check user data directory
        if hasattr(driver, 'user_data_dir'):
            print(f"User data directory: {driver.user_data_dir}")
            if os.path.exists(driver.user_data_dir):
                print("✅ User data directory exists")
            else:
                print("❌ User data directory does not exist")
        
        # Clean up
        print("Closing browser...")
        driver.quit()
        print("✅ Browser closed successfully")
        
        # Check cleanup
        if hasattr(driver, 'user_data_dir'):
            time.sleep(2)  # Wait for cleanup
            if not os.path.exists(driver.user_data_dir):
                print("✅ User data directory cleaned up successfully")
            else:
                print("❌ User data directory still exists after cleanup")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_sequential_browsers():
    """Test creating multiple browsers sequentially."""
    print("\nTesting multiple sequential browser creation...")
    
    success_count = 0
    total_tests = 3
    
    for i in range(total_tests):
        print(f"\nTest {i+1}/{total_tests}:")
        try:
            driver = setup_browser()
            print(f"  ✅ Browser {i+1} created")
            
            driver.get("https://www.google.com")
            print(f"  ✅ Browser {i+1} navigation successful")
            
            driver.quit()
            print(f"  ✅ Browser {i+1} closed")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ Browser {i+1} failed: {e}")
    
    print(f"\nSequential test results: {success_count}/{total_tests} successful")
    return success_count == total_tests

def check_temp_directory():
    """Check temp directory status."""
    print("\nChecking temp directory...")
    temp_dir = tempfile.gettempdir()
    print(f"Temp directory: {temp_dir}")
    
    # Check if we can write to temp directory
    test_file = os.path.join(temp_dir, "chrome_test_write")
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ Can write to temp directory")
        write_ok = True
    except Exception as e:
        print(f"❌ Cannot write to temp directory: {e}")
        write_ok = False
    
    # Check existing chrome directories
    chrome_dirs = [d for d in os.listdir(temp_dir) if d.startswith('chrome_user_data_')]
    print(f"Existing Chrome user data directories: {len(chrome_dirs)}")
    for d in chrome_dirs[:5]:  # Show first 5
        print(f"  - {d}")
    if len(chrome_dirs) > 5:
        print(f"  ... and {len(chrome_dirs) - 5} more")
    
    return write_ok

def main():
    """Main test function."""
    print("Simple Chrome WebDriver Test")
    print("=" * 50)
    
    # Check temp directory
    temp_ok = check_temp_directory()
    
    # Test basic browser creation
    basic_ok = test_basic_browser_creation()
    
    # Test multiple sequential browsers
    sequential_ok = test_multiple_sequential_browsers()
    
    print("\n" + "=" * 50)
    print("RESULTS:")
    print(f"Temp directory: {'✅ OK' if temp_ok else '❌ ISSUE'}")
    print(f"Basic browser: {'✅ OK' if basic_ok else '❌ FAIL'}")
    print(f"Sequential browsers: {'✅ OK' if sequential_ok else '❌ FAIL'}")
    
    if temp_ok and basic_ok and sequential_ok:
        print("\n🎉 All basic tests passed! Ready for multiprocessing tests.")
        return True
    else:
        print("\n💥 Some basic tests failed. Fix these before trying multiprocessing.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

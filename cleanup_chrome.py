#!/usr/bin/env python3
"""
Utility script to clean up leftover Chrome processes and user data directories.
Run this before testing to ensure a clean state.
"""

import os
import subprocess
import tempfile
import shutil
import signal
import psutil

def kill_chrome_processes():
    """Kill any running Chrome processes."""
    print("Killing Chrome processes...")
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Check if it's a Chrome process
            if proc.info['name'] and ('chrome' in proc.info['name'].lower() or 'chromium' in proc.info['name'].lower()):
                print(f"Killing Chrome process: PID {proc.info['pid']}, Name: {proc.info['name']}")
                proc.kill()
                killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    print(f"Killed {killed_count} Chrome processes")
    return killed_count

def cleanup_user_data_dirs():
    """Clean up any leftover Chrome user data directories."""
    print("Cleaning up Chrome user data directories...")
    temp_dir = tempfile.gettempdir()
    cleaned_count = 0
    
    try:
        for item in os.listdir(temp_dir):
            if item.startswith('chrome_user_data_'):
                dir_path = os.path.join(temp_dir, item)
                if os.path.isdir(dir_path):
                    try:
                        print(f"Removing directory: {dir_path}")
                        shutil.rmtree(dir_path)
                        cleaned_count += 1
                    except Exception as e:
                        print(f"Warning: Could not remove {dir_path}: {e}")
    except Exception as e:
        print(f"Error accessing temp directory: {e}")
    
    print(f"Cleaned up {cleaned_count} user data directories")
    return cleaned_count

def check_chrome_installation():
    """Check if Chrome/Chromium is properly installed."""
    print("Checking Chrome installation...")
    
    chrome_paths = [
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/chromium',
        '/usr/bin/chromium-browser',
        '/opt/google/chrome/chrome'
    ]
    
    found_chrome = None
    for path in chrome_paths:
        if os.path.exists(path):
            found_chrome = path
            print(f"Found Chrome at: {path}")
            break
    
    if not found_chrome:
        print("❌ Chrome/Chromium not found in standard locations")
        return False
    
    # Try to get Chrome version
    try:
        result = subprocess.run([found_chrome, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"Chrome version: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Chrome version check failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error checking Chrome version: {e}")
        return False

def check_permissions():
    """Check if we have proper permissions for temp directory."""
    print("Checking permissions...")
    temp_dir = tempfile.gettempdir()
    
    # Check if we can create and delete directories in temp
    test_dir = os.path.join(temp_dir, 'chrome_test_permissions')
    try:
        os.makedirs(test_dir, exist_ok=True)
        os.rmdir(test_dir)
        print(f"✅ Permissions OK for temp directory: {temp_dir}")
        return True
    except Exception as e:
        print(f"❌ Permission error for temp directory {temp_dir}: {e}")
        return False

def main():
    """Main cleanup function."""
    print("Chrome Cleanup Utility")
    print("=" * 50)
    
    # Step 1: Kill Chrome processes
    killed_processes = kill_chrome_processes()
    
    # Step 2: Clean up user data directories
    cleaned_dirs = cleanup_user_data_dirs()
    
    # Step 3: Check Chrome installation
    chrome_ok = check_chrome_installation()
    
    # Step 4: Check permissions
    permissions_ok = check_permissions()
    
    print("\n" + "=" * 50)
    print("CLEANUP SUMMARY:")
    print(f"Chrome processes killed: {killed_processes}")
    print(f"User data directories cleaned: {cleaned_dirs}")
    print(f"Chrome installation: {'✅ OK' if chrome_ok else '❌ ISSUE'}")
    print(f"Permissions: {'✅ OK' if permissions_ok else '❌ ISSUE'}")
    
    if chrome_ok and permissions_ok:
        print("\n✅ System is ready for Chrome WebDriver testing!")
        return True
    else:
        print("\n❌ Issues detected. Please resolve before running tests.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

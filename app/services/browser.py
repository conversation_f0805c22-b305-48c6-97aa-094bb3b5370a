import time
import gzip
import json
import os
import tempfile
import shutil
import uuid
import multiprocessing
from app.utils.helpers import extract_companies_from_response, extract_hotels_from_response, extract_prices_from_response, is_accommodation_deals_response, is_accommodation_search_response, is_advertiser_details_response

from seleniumwire import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import random

class ChromeDriverWithCleanup:
  """Wrapper for Chrome WebDriver that cleans up temporary user data directory on quit."""

  def __init__(self, driver, user_data_dir):
    self.driver = driver
    self.user_data_dir = user_data_dir
    self._is_quit = False

  def __getattr__(self, name):
    """Delegate all attribute access to the underlying driver."""
    return getattr(self.driver, name)

  def quit(self):
    """Quit the driver and clean up the temporary user data directory."""
    if self._is_quit:
      return  # Already quit, don't try again

    try:
      print(f"Quitting Chrome driver with user data dir: {self.user_data_dir}")
      self.driver.quit()
      self._is_quit = True
    except Exception as e:
      print(f"Warning: Error quitting driver: {e}")
    finally:
      # Clean up the temporary user data directory
      if self.user_data_dir and os.path.exists(self.user_data_dir):
        try:
          print(f"Cleaning up user data directory: {self.user_data_dir}")
          # Wait a moment for Chrome to fully release the directory
          time.sleep(1)
          shutil.rmtree(self.user_data_dir)
          print(f"Successfully cleaned up user data directory: {self.user_data_dir}")
        except Exception as e:
          print(f"Warning: Could not clean up user data directory {self.user_data_dir}: {e}")
          # Try to clean up individual files if directory removal fails
          try:
            for root, dirs, files in os.walk(self.user_data_dir, topdown=False):
              for file in files:
                try:
                  os.remove(os.path.join(root, file))
                except Exception:
                  pass
              for dir in dirs:
                try:
                  os.rmdir(os.path.join(root, dir))
                except Exception:
                  pass
            os.rmdir(self.user_data_dir)
            print(f"Successfully cleaned up user data directory on second attempt: {self.user_data_dir}")
          except Exception as e2:
            print(f"Final warning: Could not clean up user data directory {self.user_data_dir}: {e2}")

  def __del__(self):
    """Ensure cleanup happens even if quit() is not called explicitly."""
    if not self._is_quit:
      self.quit()

def get_random_user_agent():
  user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.4',
    'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 Edg/122.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:126.0) Gecko/20100101 Firefox/126.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 OPR/109.0.0.',
    'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Geck',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 OPR/95.0.0.',
    'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.10',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/118.',
    'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.3',
    'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.14',
  ]

  return random.choice(user_agents)

def setup_browser():
  chrome_options = Options()
  chrome_options.add_argument("--headless")
  chrome_options.add_argument("--no-sandbox")
  chrome_options.add_argument("--disable-gpu")
  chrome_options.add_argument("--disable-dev-shm-usage")
  chrome_options.add_argument("--disable-extensions")
  chrome_options.add_argument("--disable-plugins")
  chrome_options.add_argument("--disable-images")
  chrome_options.add_argument("--disable-javascript")
  chrome_options.add_argument("--disable-background-timer-throttling")
  chrome_options.add_argument("--disable-backgrounding-occluded-windows")
  chrome_options.add_argument("--disable-renderer-backgrounding")
  chrome_options.add_argument("--disable-features=TranslateUI")
  chrome_options.add_argument("--disable-ipc-flooding-protection")
  chrome_options.add_argument("--no-first-run")
  chrome_options.add_argument("--no-default-browser-check")
  chrome_options.add_argument("--disable-default-apps")
  chrome_options.add_argument("--disable-popup-blocking")
  chrome_options.add_argument("--disable-prompt-on-repost")
  chrome_options.add_argument("--disable-hang-monitor")
  chrome_options.add_argument("--disable-sync")
  chrome_options.add_argument("--disable-web-security")
  chrome_options.add_argument("--disable-features=VizDisplayCompositor")
  chrome_options.add_argument("--disable-logging")
  chrome_options.add_argument("--silent")
  chrome_options.add_argument("--log-level=3")

  # Create a unique user data directory for each process to avoid conflicts
  # Use process ID and UUID to ensure uniqueness across processes and time
  process_id = os.getpid()
  unique_id = uuid.uuid4().hex[:8]
  user_data_dir = tempfile.mkdtemp(prefix=f"chrome_user_data_{process_id}_{unique_id}_")
  chrome_options.add_argument(f"--user-data-dir={user_data_dir}")

  # Additional isolation arguments
  chrome_options.add_argument(f"--crash-dumps-dir={user_data_dir}/crashes")
  chrome_options.add_argument("--single-process")  # Run in single process mode to avoid subprocess conflicts

  # chrome_options.add_argument("--proxy-server=http://760dffea5395d2b30ea3:<EMAIL>:823")
  chrome_options.add_argument(f'user-agent={get_random_user_agent()}')
  chrome_options.add_experimental_option("prefs", {
    "profile.managed_default_content_settings.images": 2,
    "profile.default_content_setting_values.notifications": 2,
    "profile.default_content_settings.popups": 0,
    "profile.managed_default_content_settings.media_stream": 2,
  })

  # Disable logging
  chrome_options.add_experimental_option('useAutomationExtension', False)
  chrome_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])

  try:
    driver = webdriver.Chrome(options=chrome_options)
    print(f"Successfully created Chrome driver with user data dir: {user_data_dir}")
  except Exception as e:
    # If driver creation fails, clean up the user data directory
    if os.path.exists(user_data_dir):
      try:
        shutil.rmtree(user_data_dir)
      except Exception:
        pass
    raise e

  # Return wrapped driver that will clean up the user data directory on quit
  return ChromeDriverWithCleanup(driver, user_data_dir)

def search_destination(driver, destination):
  del driver.requests

  if driver.current_url != "https://www.trivago.com.br/":
    driver.get("https://www.trivago.com.br/")
  
  search_button = driver.find_element(By.CSS_SELECTOR, 'input[data-testid*="search-form-destination"]')
  driver.execute_script("arguments[0].click();", search_button)
  search_button.send_keys(destination)
  
  suggestions = []
  try:
    driver.wait_for_request("GetSearchSuggestions", timeout=10)
  except Exception as e:
    print(f"Error waiting for search suggestions: {e}")

  for req in driver.requests:
    if req.response and "GetSearchSuggestions" in req.url:
      try:
        resp_body = req.response.body
        if req.response.headers.get('Content-Encoding') == 'gzip':
          resp_body = gzip.decompress(resp_body)
        resp_json = json.loads(resp_body.decode('utf-8'))
        s_data = resp_json.get('data', {}).get('getSearchSuggestions', {}).get('unifiedSearchSuggestions', [])
        search_button.clear()

        for s in s_data:
          print(s)

          suggestions.append({
            "id": str(s["concept"]["nsid"]["id"]),
            "name": s["concept"]["translatedName"]["value"],
            "slug": s["concept"]["translatedName"]["value"].lower().replace(" ", "-"),
            "locationType": s["concept"]["typeObject"]["translatedName"]["value"],
            "location": s.get("locationLabel", "")
          })
        break
      except Exception as e:
        print(e)
        print(f"Error processing request: {e}")

  return suggestions

def click_first_more_deals_button(driver):
  try:
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')))
    more_deals_button = driver.find_element(By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')
    driver.execute_script("arguments[0].click();", more_deals_button)
  except Exception as e:
    pass

def click_more_deals_buttons(driver):
  try:
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')))
    more_deals_buttons = driver.find_elements(By.CSS_SELECTOR, 'button[data-testid*="additional-prices-slideout-entry-point"]')
    # print(f"Found {len(more_deals_buttons)} hotels")
    for btn in more_deals_buttons:
      try:
        driver.execute_script("arguments[0].click();", btn)
      except Exception as e:
        pass
  except Exception as e:
    pass

def is_last_page(driver):
  try:
    next_page_btn = driver.find_element(By.CSS_SELECTOR, 'button[data-testid*="next-result-page"]')
    if next_page_btn:
      return False
  except:
    pass
  return True

def process_driver_requests(driver):
  hotels, companies, prices = [], [], []
  for req in driver.requests:
    if req.response and req.response.status_code == 200:
      try:
        if "graphql" in req.url:
          resp_body = req.response.body
          if req.response.headers.get('Content-Encoding') == 'gzip':
            resp_body = gzip.decompress(resp_body)
          resp_json = json.loads(resp_body.decode('utf-8'))

          if is_accommodation_search_response(req):
            page_hotels = extract_hotels_from_response(resp_json)
            hotels.extend(page_hotels)
          elif is_advertiser_details_response(req):
            page_companies = extract_companies_from_response(resp_json)
            companies.extend(page_companies)
          elif is_accommodation_deals_response(req):
            page_prices = extract_prices_from_response(resp_json)
            prices.extend(page_prices)
      except Exception as e:
        print(f"Error processing request: {e}")
  return hotels, companies, prices

def get_page_info(driver, url):
  start = time.time()

  del driver.requests
  try:
    driver.get(url)
  except Exception as e:
    pass

  click_more_deals_buttons(driver)
  last = is_last_page(driver)
  hotels, companies, prices = process_driver_requests(driver)

  metrics = {
    "hotelsCount": len(hotels),
    "executionTime": time.time() - start,
    "hotels": hotels
  }

  return {"hotels": hotels, "companies": companies, "prices": prices}, metrics, last

def get_hotel_info(driver, url):
  del driver.requests
  driver.get(url)

  click_first_more_deals_button(driver)
  time.sleep(2)

  hotels, companies, prices = process_driver_requests(driver)

  return {"hotels": hotels, "companies": companies, "prices": prices}

import time

from app.services.browser import get_hotel_info, get_page_info, setup_browser
from app.utils.helpers import aggregate_hotels_prices, get_date
from app.services.trivago import get_destination_object_by_hotel_name_service

def get_hotel_prices_by_hotel_name_service(hotelName):
  if hotelName is not None:
    hotel = get_destination_object_by_hotel_name_service(hotelName)

  if hotel is None:
    return None

  prices = get_hotel_prices_service(hotel)
  filtered_prices = [price for price in prices if price.get('trivagoId') == hotel["id"]]
  
  return filtered_prices[0]

def get_hotel_prices_service(hotel, driver=None):
  try:
    settings = {"timeToTravel":3,"lengthOfStay":1}

    prices = []
    driver_created_here = driver is None
    if driver_created_here:
      driver = setup_browser()

    info = get_hotel_destination_info(driver, hotel, timeToTravel=settings["timeToTravel"], lengthOfStay=settings["lengthOfStay"])

    prices.extend(aggregate_hotels_prices(info["hotels"], info["companies"], info["prices"]))

    # Only quit the driver if we created it in this function
    if driver_created_here:
      driver.quit()
  except Exception as e:
    print(f"Error getting hotel prices: {e}")
    pass

  return prices

def get_hotel_destination_info(driver, hotel, timeToTravel=3, lengthOfStay=1):
  try:
    hotels = []
    companies = []
    prices = []

    url = f"https://www.trivago.com.br/pt-BR/srl/{hotel['slug']}?search=100-{hotel['id']};dr-{get_date(timeToTravel)}-{get_date(timeToTravel+lengthOfStay)}-s;pa-1;rc-1-2"

    hotel_info = get_hotel_info(driver, url)
    hotels.extend(hotel_info["hotels"])
    companies.extend(hotel_info["companies"])
    prices.extend(hotel_info["prices"])

    info = {
      "hotels": hotels,
      "companies": companies,
      "prices": prices
    }
  except Exception as e:
    print(f"Error getting hotel destination info: {e}")
    return {"hotels": [], "companies": [], "prices": []}

  return info
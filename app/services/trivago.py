import sys
import time

from tqdm import tqdm

from app.services.browser import get_page_info, search_destination, setup_browser
from app.utils.helpers import aggregate_hotels_prices, get_date

def get_destinations_service(destination, driver=None):
  driver_created_here = driver is None
  if driver_created_here:
    driver = setup_browser()
  suggestions = search_destination(driver, destination)

  if driver_created_here:
    driver.quit()
  return suggestions

def get_destination_object_by_hotel_name_service(hotel_name, reuse_driver=False):
  driver = setup_browser()
  suggestions = search_destination(driver, hotel_name)

  if len(suggestions) > 0:
    suggestions = suggestions[0]

  if not reuse_driver:
    driver.quit()

  return suggestions, driver

def get_destinations_by_list_service(destinations):
  driver = setup_browser()
  suggestions = []

  for destination in tqdm(destinations, desc="Processing destinations", file=sys.stdout):
    try:
      dest = search_destination(driver, destination)
      # print(f"Destino: {dest[0]}")
      suggestions.extend([dest[0]])
    except Exception as e:
      pass
      # print(f"Erro ao buscar destino {destination}: {e}")

  driver.quit()
  return suggestions

def get_hotels_prices_service(data):
  cities = data.get("cities", [])
  settings = {"timeToTravel":3,"lengthOfStay":1}

  hotelsPrices = []
  metrics = {
    "hotelsCount":0,
    "errorsCount":0,
    "executionTime": time.time(),
    "startDate": time.time(),
    "endDate": None,
    "destinations":[]
  }

  driver = setup_browser()

  processing = tqdm(cities, desc="Processing cities", file=sys.stdout)
  for city in processing:
    dest_info, dest_metrics = get_destination_info(driver, city, timeToTravel=settings["timeToTravel"], lengthOfStay=settings["lengthOfStay"])

    hotelsPrices.extend(aggregate_hotels_prices(dest_info["hotels"], dest_info["companies"], dest_info["prices"]))
    metrics["hotelsCount"] += dest_metrics["hotelsCount"]
    metrics["errorsCount"] += dest_metrics["errorsCount"]
    metrics["destinations"].append(dest_metrics)

    processing.set_description(f"{city['name']} - {len(dest_info['prices'])} - {len(dest_info['hotels'])}/{dest_metrics['hotelsCount']}")

  driver.quit()
  metrics["executionTime"] = time.time() - metrics["executionTime"]
  metrics["endDate"] = time.time()

  return hotelsPrices, metrics


def get_hotels_prices_by_destination_list_service(data):
  destinations = get_destinations_by_list_service(data["destinations"])
  settings = data.get("settings", {})

  hotelPrices, metrics = get_hotels_prices_service({"cities": destinations, "settings": settings})
  return hotelPrices, metrics

def get_destination_info(driver, city, timeToTravel=15, lengthOfStay=1):
  i = 1
  hotels = []
  companies = []
  prices = []

  metrics = {
    "name": city["name"],
    "hotelsCount":0,
    "errorsCount":0,
    "executionTime":time.time(),
    "pages":[]
  }

  base_url = f"https://www.trivago.com.br/pt-BR/srl/hotéis-{city['slug']}-brasil?search=200-{city['id']};dr-{get_date(timeToTravel)}-{get_date(timeToTravel+lengthOfStay)}-s;pa-{{page}};rc-1-2"

  while True:
    # print(f"Processando página {i} de {city['name']}")
    page_url = base_url.format(page=i)
    page_info, page_metrics, last = get_page_info(driver, page_url)
    hotels.extend(page_info["hotels"])
    companies.extend(page_info["companies"])
    prices.extend(page_info["prices"])

    metrics["hotelsCount"] += page_metrics["hotelsCount"]
    metrics["pages"].append(page_metrics)

    if last:
      break
    i += 1

  metrics["executionTime"] = time.time() - metrics["executionTime"]
  destinationInfo = {
    "hotels": hotels,
    "companies": companies,
    "prices": prices
  }

  return destinationInfo, metrics

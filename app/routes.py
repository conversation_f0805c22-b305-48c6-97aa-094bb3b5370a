import json
from multiprocessing import Pool
import os
from flask import Blueprint, request, jsonify
import pandas as pd
from tqdm import tqdm
from app.services.trivago import get_destination_object_by_hotel_name_service, get_destinations_by_list_service, get_destinations_service, get_hotels_prices_by_destination_list_service, get_hotels_prices_service
from app.services.hotel import get_hotel_prices_service
from app.services.browser import setup_browser
from dotenv import load_dotenv
import requests

load_dotenv()
main_bp = Blueprint('main', __name__)

@main_bp.route('/health', methods=['GET'])
def index():
  return jsonify({'status': 'ok'})

@main_bp.route('/destinations', methods=['GET'])
def get_destinations():
  destination = request.args.get('destination', 'São Paulo')
  suggestions = get_destinations_service(destination)
  return jsonify(suggestions)

@main_bp.route('/destinations/list', methods=['POST'])
def get_destinations_list():
  data = request.get_json()
  destinations = get_destinations_by_list_service(data["destinations"])
  return jsonify(destinations)

def process_hotel(hotel):
  driver = setup_browser()

  try:
    hotel_prices = get_hotel_prices_service(hotel, driver=driver)
    filtered_prices = [price for price in hotel_prices if price.get('trivagoId') == str(hotel["id"])]
    if filtered_prices:
      return filtered_prices[0]
    return None
  except Exception:
    return None
  finally:
    driver.quit()

@main_bp.route('/upload/hotels', methods=['POST'])
def upload_hotels_csv():
  if 'file' not in request.files:
    return jsonify({'error': 'Missing file or column name'}), 400
  
  file = request.files['file']
  hotel_trivago_id_column = 'Trivago Item ID'
  hotel_name_column = 'Trivago Hotel Name'

  if file.filename == '' or not file.filename.endswith('.csv'):
    return jsonify({'error': 'Invalid file format'}), 400

  hotels = []
  try:
    df = pd.read_csv(file, delimiter=';')
    if hotel_name_column not in df.columns:
      return jsonify({'error': f'Column {hotel_name_column} not found'}), 400

    filters_str = request.form.get('filters', '').strip()
    if filters_str:
      try:
        filters_data = json.loads(filters_str)
      except json.JSONDecodeError:
        return jsonify({'error': 'O parâmetro filters não é um JSON válido'}), 400

      if not isinstance(filters_data, dict) or 'filters' not in filters_data:
        return jsonify({'error': 'Formato inválido para filtros. Esperado {"filters": [{...}]}' }), 400

      for f in filters_data['filters']:
        col = f.get('column')
        val = f.get('value')
        if not col or val is None:
          return jsonify({'error': 'Cada filtro deve conter "column" e "value"'}), 400

        if col not in df.columns:
          return jsonify({'error': f'Coluna {col} não encontrada no CSV'}), 400

        df = df[df[col] == val]

    for _, row in df.iterrows():
      if pd.notna(row[hotel_name_column]):
        hotels.append({
          'id': row[hotel_trivago_id_column],
          'name': row[hotel_name_column],
          'slug': row[hotel_name_column].lower().replace(' ', '-')
        })

    send_update_request(request.form.get('runId'), len(hotels), str(filters_data['filters']))
  except Exception as e:
    print('error:', e)
    print(os.environ['TIMI_SERVICE_URL'])
    return jsonify({'error': str(e)}), 500

  prices = []
  all_prices = []
  processed_hotels = 0
  with Pool(processes=2) as pool:
    for result in tqdm(pool.imap_unordered(process_hotel, hotels), total=len(hotels), desc="Processing hotels"):
      processed_hotels += 1

      if result is not None:
        prices.append(result)
        all_prices.append(result)

        if len(prices) >= request.form.get('batchSize', type=int):
          send_batch_request(request.form.get('runId'), prices, last=False, processed_hotels=processed_hotels)
          prices = []

  send_batch_request(request.form.get('runId'), prices, last=True, processed_hotels=processed_hotels)

  print(f"Returned {len(all_prices)}/{len(hotels)} hotels prices")
  return jsonify(all_prices)

def send_batch_request(run_id, prices, last=False, processed_hotels=0):
  try:
    batch_url = os.environ['TIMI_SERVICE_URL'] + '/batch'
    response = requests.post(batch_url, json={'runId': run_id, 'batchSize': len(prices), 'prices': prices, 'last': last, 'processedHotels': processed_hotels})
    response.raise_for_status()
  except requests.exceptions.RequestException as e:
    print(f"Error sending batch request: {e}")

def send_update_request(run_id, hotels_count, filters):
  try:
    update_url = os.environ['TIMI_SERVICE_URL'] + '/run'
    response = requests.put(update_url, json={'runId': run_id, 'hotelsCount': hotels_count, 'filters': filters})
    response.raise_for_status()
  except requests.exceptions.RequestException as e:
    print(f"Error sending update request: {e}")

@main_bp.route('/hotel/prices', methods=['POST'])
def get_hotel_prices_by_hotel_name():
  data = request.get_json()
  hotelName = data.get("hotelName")

  if hotelName is not None:
    hotel = get_destination_object_by_hotel_name_service(hotelName)

  if len(hotel) == 0:
    return jsonify({ "error": "Hotel not found in search" }), 404

  prices = get_hotel_prices_service(hotel)
  if len(prices) == 0:
    return jsonify({ "error": "Hotel not found in page" }), 404

  filtered_prices = [price for price in prices if price.get('trivagoId') == hotel[0]["id"]]

  if len(filtered_prices) == 0:
    return jsonify({ "error": "Hotel not found" }), 404

  return jsonify(filtered_prices[0])


@main_bp.route('/hotels/prices', methods=['POST'])
def get_hotels_prices_by_hotel_name_list():
  hotels = request.get_json()
  prices = []
  driver = None

  try:
    for hotel in tqdm(hotels, desc="Processing hotels"):
      hotel_found, driver = get_destination_object_by_hotel_name_service(hotel, reuse_driver=True)

      try:
        hotel_prices = get_hotel_prices_service(hotel_found, driver)
        filtered_prices = [price for price in hotel_prices if price.get('trivagoId') == hotel_found["id"]]

        prices.append(filtered_prices[0])

      except Exception as e:
        print(f"Hotel {hotel} not found or does not have prices", e)
  finally:
    # Clean up the driver if it was created
    if driver:
      driver.quit()

  return jsonify(prices)

@main_bp.route('/destination/prices', methods=['POST'])
def get_hotels_prices_by_destination_list():
  data = request.get_json()
  prices, metrics = get_hotels_prices_by_destination_list_service(data)
  return jsonify({ "hotelsPrices": prices, "metrics": metrics })

@main_bp.route('/prices', methods=['POST'])
def get_hotels_prices():
  data = request.get_json()
  prices, metrics = get_hotels_prices_service(data)
  return jsonify({ "hotelsPrices": prices, "metrics": metrics })
